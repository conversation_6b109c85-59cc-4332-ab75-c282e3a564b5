import React, { useState, useEffect } from "react";
import { Card, Collapse, List, Spin, Alert, Tag, Typography } from "antd";
import { motion } from "framer-motion";
import {
  BugOutlined,
  ClockCircleOutlined,
  DisconnectOutlined,
  SearchOutlined,
  ExclamationCircleOutlined,
  GlobalOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  CompassOutlined,
  LockOutlined,
} from "@ant-design/icons";
import { ErrorCategoryDetail, FilterCriteria } from "@/types";
import apiService from "@/services/api";

const { Panel } = Collapse;
const { Text, Title } = Typography;

interface ErrorCategoriesWidgetProps {
  filters?: FilterCriteria;
  loading?: boolean;
}

const ErrorCategoriesWidget: React.FC<ErrorCategoriesWidgetProps> = ({
  filters,
  loading: parentLoading = false,
}) => {
  const [errorCategories, setErrorCategories] = useState<ErrorCategoryDetail[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadErrorCategories();
  }, [filters]);

  const loadErrorCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getErrorCategoriesWithTests(filters);
      setErrorCategories(data || []); // Ensure we always have an array
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load error categories";
      setError(errorMessage);
      setErrorCategories([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "timeout":
        return <ClockCircleOutlined className="text-orange-500" />;
      case "connection":
        return <DisconnectOutlined className="text-red-500" />;
      case "element not found":
        return <SearchOutlined className="text-blue-500" />;
      case "assertion":
        return <ExclamationCircleOutlined className="text-purple-500" />;
      case "network":
        return <GlobalOutlined className="text-green-500" />;
      case "navigation":
        return <CompassOutlined className="text-indigo-500" />;
      case "permission":
        return <LockOutlined className="text-yellow-500" />;
      default:
        return <QuestionCircleOutlined className="text-gray-500" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "timeout":
        return "orange";
      case "connection":
        return "red";
      case "element not found":
        return "blue";
      case "assertion":
        return "purple";
      case "network":
        return "green";
      case "navigation":
        return "cyan";
      case "permission":
        return "gold";
      default:
        return "default";
    }
  };

  if (loading || parentLoading) {
    return (
      <Card title="Error Categories" className="h-80">
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card title="Error Categories" className="h-80">
        <Alert
          message="Error Loading Categories"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!errorCategories || errorCategories.length === 0) {
    return (
      <Card title="Error Categories" className="h-80">
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <CheckCircleOutlined className="text-4xl text-green-500 mb-4" />
            <p className="text-gray-500">No errors found!</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="Error Categories" className="h-80">
      <div className="h-full overflow-y-auto">
        <Collapse
          ghost
          size="small"
          items={(errorCategories || []).map((category, index) => ({
            key: index.toString(),
            label: (
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(category.category)}
                  <span className="font-medium">{category.category}</span>
                </div>
                <Tag
                  color={getCategoryColor(category.category)}
                  className="ml-2"
                >
                  {category.count} test{category.count !== 1 ? "s" : ""}
                </Tag>
              </div>
            ),
            children: (
              <div className="pl-6">
                <List
                  size="small"
                  dataSource={category.failedTests.slice(0, 10)} // Show top 10 failed tests
                  renderItem={(test) => (
                    <List.Item className="border-none py-1">
                      <div className="w-full">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <Text
                              className="text-sm font-medium text-gray-800 block truncate"
                              title={test.testName}
                            >
                              {test.testName}
                            </Text>
                            <Text
                              className="text-xs text-gray-500 block truncate"
                              title={test.filePath}
                            >
                              {test.filePath}
                            </Text>
                          </div>
                          <div className="flex items-center gap-2 ml-2">
                            <Tag color="red" size="small">
                              {test.failRate.toFixed(1)}% fail
                            </Tag>
                            <Text className="text-xs text-gray-400">
                              {test.projectName}
                            </Text>
                          </div>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
                {category.failedTests.length > 10 && (
                  <div className="text-center mt-2">
                    <Text className="text-xs text-gray-500">
                      ... and {category.failedTests.length - 10} more tests
                    </Text>
                  </div>
                )}
              </div>
            ),
          }))}
        />
      </div>
    </Card>
  );
};

export default ErrorCategoriesWidget;
