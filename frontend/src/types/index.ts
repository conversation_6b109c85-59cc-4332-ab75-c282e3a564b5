// API Response Types
export interface TestAnalytics {
  serialNumber: number;
  testName: string;
  filePath: string;
  passRate: number;
  failRate: number;
  flakyRate: number;
  skippedRate: number;
  totalRuns: number;
  testCreatedDate: string;
  testUpdatedDate: string;
  averageDuration: number;
  retryCount: number;
  projectName: string;
  tags: string[];
  priority: string;
  errorMessages: string[];
  lastStatus: string;
  videoS3Key?: string;
  videoUrl?: string;
  screenshotS3Keys?: string[];
  screenshotUrls?: string[];
  allAttachmentS3Keys?: AttachmentInfo[];
}

export interface AttachmentInfo {
  name: string;
  type: string;
  contentType: string;
  fileName: string;
  s3Key: string;
  path: string;
}

export interface ErrorCategoryDetail {
  category: string;
  count: number;
  failedTests: TestAnalytics[];
}

// Test Run Types for detailed view
export interface TestRun {
  id: string;
  serialNumber: number;
  testName: string;
  filePath: string;
  projectName: string;
  status: string;
  duration: number;
  startTime: string;
  workerIndex: number;
  parallelIndex: number;
  retry: number;
  tags: string[];
  priority: string;
  expectedStatus: string;
  fileName: string; // Source JSON file name
  steps: TestStep[];
  errors: TestError[];
  stdout: TestOutput[];
  stderr: TestOutput[];
  attachments: TestAttachment[];
  annotations: TestAnnotation[];

  // S3-related fields
  videoS3Key?: string;
  videoUrl?: string;
  screenshotS3Keys?: string[];
  screenshotUrls?: string[];
  allAttachmentS3Keys?: AttachmentInfo[];
}

export interface TestStep {
  title: string;
  duration: number;
  error?: TestError;
}

export interface TestError {
  message: string;
  stack?: string;
  location?: TestLocation;
  snippet?: string;
}

export interface TestOutput {
  text: string;
}

export interface TestAttachment {
  name: string;
  contentType: string;
  path: string;
  s3Key?: string;
  url?: string;
}

export interface TestAnnotation {
  type: string;
  description: string;
}

export interface TestLocation {
  file: string;
  line: number;
  column: number;
}

export interface DashboardMetrics {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  overallPassRate: number;
  overallFailRate: number;
  averageDuration: number;
  totalDuration: number;
  testsByProject: Record<string, number>;
  testsByStatus: Record<string, number>;
  testsByPriority: Record<string, number>;
  trendData: TrendPoint[];
  errorCategories: Record<string, number>;
  topFailingTests: TestAnalytics[];
  slowestTests: TestAnalytics[];
  mostRetriedTests: TestAnalytics[];
  filePathMetrics: Record<string, FileMetrics>;
}

export interface TrendPoint {
  date: string;
  passRate: number;
  failRate: number;
  testCount: number;
  duration: number;
}

export interface FileMetrics {
  filePath: string;
  testCount: number;
  passRate: number;
  failRate: number;
  avgDuration: number;
}

export interface FilterCriteria {
  startDate?: string;
  endDate?: string;
  status?: string[];
  projectName?: string[];
  filePath?: string[];
  tags?: string[];
  selectedDate?: string; // For nightly run date filter
}

// S3 Configuration Types
export interface S3Status {
  bucket?: string;
  connected: boolean;
  schedulerRunning?: boolean;
  processedFiles?: number;
  timestamp?: string;
  s3Enabled: boolean;
  message?: string;
}

export interface S3Config {
  enabled: boolean;
  bucket: string;
  prefix: string;
  syncInterval: number;
}

export interface ChartData {
  labels: string[];
  datasets: Dataset[];
  type: string;
  title: string;
}

export interface Dataset {
  label: string;
  data: number[];
  backgroundColor?: string[];
  borderColor?: string[];
  fill?: boolean;
}

export interface WordCloudData {
  text: string;
  value: number;
  color?: string;
}

export interface SunburstData {
  name: string;
  value: number;
  children?: SunburstData[];
  color?: string;
}

export interface BubbleChartData {
  x: number;
  y: number;
  r: number;
  label: string;
  color?: string;
}

export interface HeatmapData {
  x: string;
  y: string;
  value: number;
  color?: string;
}

// UI Component Types
export interface FilterOptions {
  status: string[]; // Should include "flaky" for flaky tests
  projectName: string[]; // Should only include "guac" and "zGPU"
  tags: string[];
}

export interface FileInfo {
  name: string;
  size: string;
  type: string;
}

export interface UploadResponse {
  message: string;
  fileName: string;
  fileId: string;
}

export interface MultipleUploadResponse {
  uploadedFiles: string[];
  uploadedCount: number;
  totalFiles: number;
  errors?: string[];
  errorCount?: number;
}

export interface JSONUploadRequest {
  jsonContent: string;
  fileName?: string;
}

export interface JSONUploadResponse {
  message: string;
  fileName: string;
  fileId: string;
  valid: boolean;
  error?: string;
}

// Chart Configuration Types
export interface ChartConfig {
  type: "line" | "bar" | "pie" | "doughnut" | "radar" | "bubble" | "scatter";
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: {
    legend?: {
      display: boolean;
      position?: "top" | "bottom" | "left" | "right";
    };
    tooltip?: {
      enabled: boolean;
    };
  };
  scales?: {
    x?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
    y?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
  };
}

// Table Column Types
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  sorter?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
  filters?: Array<{ text: string; value: string }>;
  onFilter?: (value: string, record: any) => boolean;
}

// Navigation Types
export interface MenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  path?: string;
  children?: MenuItem[];
}

// Theme Types
export interface ThemeConfig {
  primaryColor: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
  backgroundColor: string;
  cardBackground: string;
  textColor: string;
  borderColor: string;
}

// API Error Types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

// Loading States
export interface LoadingState {
  dashboard: boolean;
  tests: boolean;
  charts: boolean;
  files: boolean;
  upload: boolean;
}

// Form Types
export interface DateRangeValue {
  startDate: Date | null;
  endDate: Date | null;
}

export interface SelectOption {
  label: string;
  value: string;
}

// Visualization Types
export interface VisualizationProps {
  data: any;
  loading?: boolean;
  error?: string;
  height?: number;
  width?: number;
}

// Slack notification types
export interface SlackNotificationRequest {
  timeRange: string;
  startDate?: string;
  endDate?: string;
}

export interface SlackNotificationResponse {
  message: string;
  metrics: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    flakyTests: number;
    passRate: number;
  };
}

// Export all types for easy importing
export type {
  TestAnalytics as Test,
  DashboardMetrics as Dashboard,
  ChartData as Chart,
  FilterCriteria as Filters,
};
