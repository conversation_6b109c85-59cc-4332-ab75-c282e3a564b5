package services

import (
	"cbi-e2e-analytics/domain/entities"
	"cbi-e2e-analytics/domain/valueobjects"
	"fmt"
	"log"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"
)

// AnalyticsService provides domain logic for test analytics
type AnalyticsService struct{}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService() *AnalyticsService {
	return &AnalyticsService{}
}

// CalculateTestAnalytics calculates analytics for individual tests based on Playwright JSON format
// Tests are consolidated by testName + projectName combination across all JSON runs
func (s *AnalyticsService) CalculateTestAnalytics(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.TestAnalytics {
	testMap := make(map[string]*valueobjects.TestAnalytics)
	serialNumber := 1

	for _, result := range testResults {

		s.processPlaywrightTestResultConsolidated(result, testMap, filters)
	}

	// Convert map to slice and apply status and priority filtering
	analytics := make([]valueobjects.TestAnalytics, 0, len(testMap))

	var skippedEmptyRuns int
	var skippedByStatus int
	var skippedByProject int



	for _, ta := range testMap {
		// Skip tests that have no valid runs (all runs were filtered out by date)
		if len(ta.RunStatusMap) == 0 {
			skippedEmptyRuns++
			continue
		}

		// Apply status filter at the test level (after calculating lastStatus)
		if len(filters.Status) > 0 {
			found := false
			for _, status := range filters.Status {
				if ta.LastStatus == status {
					found = true
					break
				}
			}
			if !found {
				skippedByStatus++
				continue // Skip this test if it doesn't match the status filter
			}
		}

		// Apply project filter at the test level (after calculating project name)
		if len(filters.ProjectName) > 0 {
			found := false
			for _, project := range filters.ProjectName {
				if strings.EqualFold(ta.ProjectName, project) {
					found = true
					break
				}
			}
			if !found {
				skippedByProject++
				continue // Skip this test if it doesn't match the project filter
			}
		}

		ta.SerialNumber = serialNumber
		serialNumber++
		analytics = append(analytics, *ta)
	}





	// Sort by test name
	sort.Slice(analytics, func(i, j int) bool {
		return analytics[i].TestName < analytics[j].TestName
	})

	return analytics
}

// CalculateTestRuns extracts individual test run data from Playwright JSON format
func (s *AnalyticsService) CalculateTestRuns(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.TestRun {
	var testRuns []valueobjects.TestRun
	serialNumber := 1

	for _, result := range testResults {
		// Process each suite in the test result
		for _, suite := range result.Suites {
			s.processTestRunsFromSuite(suite, result, &testRuns, &serialNumber, filters)
		}
	}

	return testRuns
}

// isTestFlaky determines if a test is flaky using the same logic as analytics
func (s *AnalyticsService) isTestFlaky(test entities.Test) bool {
	// Primary check: if test status is explicitly "flaky"
	if test.Status == "flaky" {
		return true
	}

	// Secondary check: analyze multiple results with different outcomes
	if len(test.Results) > 1 {
		statusCounts := make(map[string]int)
		for _, result := range test.Results {
			normalizedStatus := result.Status
			// Apply status normalization
			if normalizedStatus == "expected" {
				normalizedStatus = "passed"
			} else if normalizedStatus == "unexpected" || normalizedStatus == "timeout" || normalizedStatus == "timedOut" {
				normalizedStatus = "failed"
			}
			statusCounts[normalizedStatus]++
		}

		// If we have multiple different statuses and at least one pass, it's flaky
		if len(statusCounts) > 1 && statusCounts["passed"] > 0 {
			return true
		}
	}

	return false
}

// GetAvailableDates extracts unique dates from test results for the last 5 days
func (s *AnalyticsService) GetAvailableDates(testResults []entities.TestResult) []string {
	dateSet := make(map[string]bool)

	// Get current time and 5 days ago
	now := time.Now().UTC()
	fiveDaysAgo := now.AddDate(0, 0, -5)

	for _, result := range testResults {
		// Use stats.startTime for date extraction
		startTime := result.Stats.StartTime

		// Only include dates from the last 5 days
		if startTime.After(fiveDaysAgo) && startTime.Before(now.Add(24*time.Hour)) {
			dateKey := startTime.Format("2006-01-02")
			dateSet[dateKey] = true
		}
	}

	// Convert to slice and sort (most recent first)
	var dates []string
	for date := range dateSet {
		dates = append(dates, date)
	}

	sort.Slice(dates, func(i, j int) bool {
		return dates[i] > dates[j] // Most recent first
	})

	return dates
}

// processTestRunsFromSuite recursively processes suites and extracts test runs
func (s *AnalyticsService) processTestRunsFromSuite(suite entities.Suite, result entities.TestResult, testRuns *[]valueobjects.TestRun, serialNumber *int, filters *valueobjects.FilterCriteria) {
	s.processTestRunsFromSuiteWithTitle(suite, "", result, testRuns, serialNumber, filters)
}

// processTestRunsFromSuiteWithTitle recursively processes suites and extracts test runs with suite title context
func (s *AnalyticsService) processTestRunsFromSuiteWithTitle(suite entities.Suite, parentSuiteTitle string, result entities.TestResult, testRuns *[]valueobjects.TestRun, serialNumber *int, filters *valueobjects.FilterCriteria) {
	// Skip coverage setup/teardown projects
	if strings.Contains(suite.File, "coverage.setup") || strings.Contains(suite.File, "coverage.teardown") {
		return
	}

	// Determine the current suite title for specs
	currentSuiteTitle := suite.Title
	if parentSuiteTitle != "" {
		currentSuiteTitle = parentSuiteTitle + " > " + suite.Title
	}

	// Process specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}
		s.processTestRunsFromSpecWithSuite(spec, currentSuiteTitle, result, testRuns, serialNumber, filters)
	}

	// Process nested suites
	for _, nestedSuite := range suite.Suites {
		s.processTestRunsFromSuiteWithTitle(nestedSuite, currentSuiteTitle, result, testRuns, serialNumber, filters)
	}
}

// processTestRunsFromSpecWithSuite processes individual test specs and extracts runs with suite title context
func (s *AnalyticsService) processTestRunsFromSpecWithSuite(spec entities.Spec, suiteTitle string, result entities.TestResult, testRuns *[]valueobjects.TestRun, serialNumber *int, filters *valueobjects.FilterCriteria) {
	for _, test := range spec.Tests {
		// Determine if this test is flaky using the same logic as analytics
		isFlaky := s.isTestFlaky(test)

		// Each result in test.Results represents a separate run (including retries)
		for _, execution := range test.Results {
			// Apply filters with flaky detection
			if !s.matchesTestRunFiltersWithFlaky(spec, test, execution, result.Stats.StartTime, filters, isFlaky) {
				continue
			}



			// Convert steps
			steps := make([]valueobjects.TestRunStep, len(execution.Steps))
			for i, step := range execution.Steps {
				var stepError *valueobjects.TestRunError
				if step.Error != nil {
					stepError = &valueobjects.TestRunError{
						Message: step.Error.Message,
						Stack:   step.Error.Stack,
						Snippet: step.Error.Snippet,
					}
					if step.Error.Location != nil {
						stepError.Location = &valueobjects.TestRunLocation{
							File:   step.Error.Location.File,
							Line:   step.Error.Location.Line,
							Column: step.Error.Location.Column,
						}
					}
				}
				steps[i] = valueobjects.TestRunStep{
					Title:    step.Title,
					Duration: step.Duration,
					Error:    stepError,
				}
			}

			// Convert errors
			errors := make([]valueobjects.TestRunError, len(execution.Errors))
			for i, err := range execution.Errors {
				errors[i] = valueobjects.TestRunError{
					Message: err.Message,
					Stack:   err.Stack,
					Snippet: err.Snippet,
				}
				if err.Location != nil {
					errors[i].Location = &valueobjects.TestRunLocation{
						File:   err.Location.File,
						Line:   err.Location.Line,
						Column: err.Location.Column,
					}
				}
			}

			// Convert stdout
			stdout := make([]valueobjects.TestRunOutput, len(execution.Stdout))
			for i, out := range execution.Stdout {
				stdout[i] = valueobjects.TestRunOutput{Text: out.Text}
			}

			// Convert stderr
			stderr := make([]valueobjects.TestRunOutput, len(execution.Stderr))
			for i, out := range execution.Stderr {
				stderr[i] = valueobjects.TestRunOutput{Text: out.Text}
			}

			// Convert attachments
			attachments := make([]valueobjects.TestRunAttachment, len(execution.Attachments))
			for i, att := range execution.Attachments {
				attachments[i] = valueobjects.TestRunAttachment{
					Name:        att.Name,
					ContentType: att.ContentType,
					Path:        att.Path,
				}
			}

			// Convert annotations
			annotations := make([]valueobjects.TestRunAnnotation, len(test.Annotations))
			for i, ann := range test.Annotations {
				annotations[i] = valueobjects.TestRunAnnotation{
					Type:        ann.Type,
					Description: ann.Description,
				}
			}

			// Create combined test name: Suite Title > Spec Title
			combinedTestName := spec.Title
			if suiteTitle != "" {
				combinedTestName = suiteTitle + " > " + spec.Title
			}

			// Process S3 paths for videos and screenshots
			// Pass test context to help construct correct S3 keys
			testInfo := map[string]interface{}{
				"testName":    combinedTestName,
				"specTitle":   spec.Title,
				"suiteTitle":  suiteTitle,
				"retry":       execution.Retry,
				"workerIndex": execution.WorkerIndex,
			}

			// Use the correct run folder name from S3 data source instead of extracting from filename
			var videoS3Key string
			var screenshotS3Keys []string
			var allAttachments []map[string]string

			if result.RunFolderName != "" {
				// Use run folder name for all attachments when available from S3 data
				videoS3Key = s.extractVideoS3KeyWithRunFolder(execution.Attachments, result.RunFolderName, testInfo)
				screenshotS3Keys = s.extractScreenshotS3KeysWithRunFolder(execution.Attachments, result.RunFolderName)
				allAttachments = s.extractAllAttachmentS3Keys(execution.Attachments, result.RunFolderName)
			} else {
				// Fallback to filename-based extraction for locally uploaded files
				videoS3Key = s.extractVideoS3KeyWithTestInfo(execution.Attachments, result.FileName, testInfo)
				screenshotS3Keys = s.extractScreenshotS3Keys(execution.Attachments, result.FileName)
				allAttachments = s.extractAllAttachmentsForTest(execution.Attachments, result.FileName)
			}

			log.Printf("Test: %s, Status: %s, VideoS3Key: %s, ScreenshotS3Keys: %v, AllAttachments: %d",
				combinedTestName, execution.Status, videoS3Key, screenshotS3Keys, len(allAttachments))

			// Determine the correct status (flaky if detected, otherwise normalized status)
			var finalStatus string
			if isFlaky {
				finalStatus = "flaky"
			} else {
				finalStatus = execution.Status
				// Apply status normalization
				if finalStatus == "expected" {
					finalStatus = "passed"
				} else if finalStatus == "unexpected" || finalStatus == "timeout" || finalStatus == "timedOut" {
					finalStatus = "failed"
				}
			}

			// Create test run
			testRun := valueobjects.TestRun{
				ID:             fmt.Sprintf("%s-%d-%d", result.ID, execution.WorkerIndex, execution.Retry),
				SerialNumber:   *serialNumber,
				TestName:       combinedTestName,
				FilePath:       spec.File,
				ProjectName:    test.ProjectName,
				Status:         finalStatus,
				Duration:       execution.Duration,
				StartTime:      execution.StartTime,
				WorkerIndex:    execution.WorkerIndex,
				ParallelIndex:  execution.ParallelIndex,
				Retry:          execution.Retry,
				Tags:           spec.Tags,
				ExpectedStatus: test.ExpectedStatus,
				FileName:       result.FileName,
				Steps:          steps,
				Errors:         errors,
				Stdout:         stdout,
				Stderr:         stderr,
				Attachments:    attachments,
				Annotations:    annotations,
				ScreenshotS3Keys: screenshotS3Keys,
			}

			// Add all attachments with S3 keys
			if len(allAttachments) > 0 {
				testRun.AllAttachmentS3Keys = allAttachments
			}

			// Only set VideoS3Key if it's not empty
			if videoS3Key != "" {
				testRun.VideoS3Key = videoS3Key
			}

			*testRuns = append(*testRuns, testRun)
			*serialNumber++
		}
	}
}

// matchesTestRunFiltersWithFlaky checks if a test run matches the given filters with flaky detection
func (s *AnalyticsService) matchesTestRunFiltersWithFlaky(spec entities.Spec, test entities.Test, execution entities.TestExecution, statsStartTime time.Time, filters *valueobjects.FilterCriteria, isFlaky bool) bool {
	if filters == nil {
		return true
	}

	// Date range filter (using stats.startTime)
	if filters.StartDate != nil && statsStartTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && statsStartTime.After(*filters.EndDate) {
		return false
	}

	// Status filter with flaky detection
	if len(filters.Status) > 0 {
		statusMatch := false
		for _, status := range filters.Status {
			var testStatus string
			if isFlaky {
				testStatus = "flaky"
			} else {
				testStatus = execution.Status
				// Apply status normalization
				if testStatus == "expected" {
					testStatus = "passed"
				} else if testStatus == "unexpected" || testStatus == "timeout" || testStatus == "timedOut" {
					testStatus = "failed"
				}
			}

			if testStatus == status {
				statusMatch = true
				break
			}
		}
		if !statusMatch {
			return false
		}
	}

	// Project filter
	if len(filters.ProjectName) > 0 {
		projectMatch := false
		for _, project := range filters.ProjectName {
			fmt.Printf("🔍 Comparing project: '%s' with filter: '%s'\n", test.ProjectName, project)
			if strings.EqualFold(test.ProjectName, project) {
				projectMatch = true
				fmt.Printf("✅ Project match found: '%s'\n", test.ProjectName)
				break
			}
		}
		if !projectMatch {
			fmt.Printf("❌ Project filter failed for: '%s'\n", test.ProjectName)
			return false
		}
	}

	// File path filter
	if len(filters.FilePath) > 0 {
		fileMatch := false
		for _, filePath := range filters.FilePath {
			if strings.Contains(spec.File, filePath) {
				fileMatch = true
				break
			}
		}
		if !fileMatch {
			return false
		}
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		tagMatch := false
		for _, filterTag := range filters.Tags {
			for _, specTag := range spec.Tags {
				if strings.EqualFold(specTag, filterTag) {
					tagMatch = true
					break
				}
			}
			if tagMatch {
				break
			}
		}
		if !tagMatch {
			return false
		}
	}

	return true
}

// matchesTestRunFilters checks if a test run matches the given filters
func (s *AnalyticsService) matchesTestRunFilters(spec entities.Spec, test entities.Test, execution entities.TestExecution, statsStartTime time.Time, filters *valueobjects.FilterCriteria) bool {
	if filters == nil {
		return true
	}

	// Date range filter (using stats.startTime)
	if filters.StartDate != nil && statsStartTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && statsStartTime.After(*filters.EndDate) {
		return false
	}

	// Status filter
	if len(filters.Status) > 0 {
		statusMatch := false
		for _, status := range filters.Status {
			if execution.Status == status {
				statusMatch = true
				break
			}
		}
		if !statusMatch {
			return false
		}
	}

	// Project filter
	if len(filters.ProjectName) > 0 {
		projectMatch := false
		for _, project := range filters.ProjectName {
			fmt.Printf("🔍 Comparing project: '%s' with filter: '%s'\n", test.ProjectName, project)
			if strings.EqualFold(test.ProjectName, project) {
				projectMatch = true
				fmt.Printf("✅ Project match found: '%s'\n", test.ProjectName)
				break
			}
		}
		if !projectMatch {
			fmt.Printf("❌ Project filter failed for: '%s'\n", test.ProjectName)
			return false
		}
	}

	// File path filter
	if len(filters.FilePath) > 0 {
		fileMatch := false
		for _, filePath := range filters.FilePath {
			if strings.Contains(spec.File, filePath) {
				fileMatch = true
				break
			}
		}
		if !fileMatch {
			return false
		}
	}



	// Tags filter
	if len(filters.Tags) > 0 {
		tagMatch := false
		for _, filterTag := range filters.Tags {
			for _, specTag := range spec.Tags {
				if specTag == filterTag {
					tagMatch = true
					break
				}
			}
			if tagMatch {
				break
			}
		}
		if !tagMatch {
			return false
		}
	}

	return true
}

// CalculateDashboardMetrics calculates overall dashboard metrics based on Playwright JSON format
func (s *AnalyticsService) CalculateDashboardMetrics(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) valueobjects.DashboardMetrics {
	metrics := valueobjects.DashboardMetrics{
		TestsByProject:  make(map[string]int),
		TestsByStatus:   make(map[string]int),
		ErrorCategories: make(map[string]int),
		FilePathMetrics: make(map[string]valueobjects.FileMetrics),
	}

	var allExecutions []testExecution
	trendMap := make(map[string]*valueobjects.TrendPoint)

	// Use consolidated test analytics to calculate dashboard metrics
	// This ensures unique test counts (not total occurrences across runs)
	testAnalytics := s.CalculateTestAnalytics(testResults, filters)

	// Count unique tests by status and project
	var totalExpected, totalSkipped, totalUnexpected, totalFlaky int
	var totalTestDuration float64
	projectCounts := make(map[string]int)
	uniqueTestNames := make(map[string]bool) // Track unique test names for dashboard count

	for _, ta := range testAnalytics {
		// Count by project (each test+project combination counts as 1)
		if ta.ProjectName != "" {
			projectCounts[ta.ProjectName]++
		}

		// Track unique test names for dashboard total count (regardless of project)
		uniqueTestNames[ta.TestName] = true

		// Count by last status for dashboard metrics
		switch ta.LastStatus {
		case "passed":
			totalExpected++
		case "failed":
			totalUnexpected++
		case "flaky":
			totalFlaky++
		case "skipped":
			totalSkipped++
		}

		// Accumulate duration
		totalTestDuration += ta.AverageDuration
	}

	// Set dashboard metrics based on unique test+project combinations
	// This ensures consistency: each test running on each project counts as 1
	metrics.TotalTests = len(testAnalytics)   // Count unique test+project combinations
	metrics.PassedTests = totalExpected      // Count of unique test+project combinations with passed status
	metrics.FailedTests = totalUnexpected    // Count of unique test+project combinations with failed status
	metrics.SkippedTests = totalSkipped      // Count of unique test+project combinations with skipped status

	// Set TestsByStatus for compatibility with existing frontend
	statusCounts := make(map[string]int)
	statusCounts["passed"] = totalExpected
	statusCounts["failed"] = totalUnexpected
	statusCounts["skipped"] = totalSkipped
	statusCounts["flaky"] = totalFlaky
	metrics.TestsByStatus = statusCounts

	// Calculate average duration from consolidated test analytics
	if len(testAnalytics) > 0 {
		metrics.AverageDuration = totalTestDuration / float64(len(testAnalytics))
	}
	metrics.TotalDuration = totalTestDuration

	metrics.TestsByProject = projectCounts

	// Still need executions for trend data and error categories
	for _, result := range testResults {
		if !s.matchesDateFilters(result.Stats.StartTime, filters) {
			continue
		}

		executions := s.extractPlaywrightTestExecutions(result, filters)
		allExecutions = append(allExecutions, executions...)

		for _, exec := range executions {
			// Update error categories
			if exec.Error != nil {
				category := s.categorizeError(exec.Error.Message)
				metrics.ErrorCategories[category]++
			}

			// Update trend data based on stats.startTime
			dateKey := exec.StartTime.Format("2006-01-02")
			if trendPoint, exists := trendMap[dateKey]; exists {
				trendPoint.TestCount++
				if exec.Status == "passed" || exec.Status == "expected" {
					trendPoint.PassRate = (trendPoint.PassRate*float64(trendPoint.TestCount-1) + 1) / float64(trendPoint.TestCount)
				}
				trendPoint.Duration += float64(exec.Duration)
			} else {
				passRate := 0.0
				if exec.Status == "passed" || exec.Status == "expected" {
					passRate = 1.0
				}
				trendMap[dateKey] = &valueobjects.TrendPoint{
					Date:      exec.StartTime,
					PassRate:  passRate,
					TestCount: 1,
					Duration:  float64(exec.Duration),
				}
			}
		}
	}

	// Calculate derived metrics using the stats-based counts (already set above)
	if metrics.TotalTests > 0 {
		metrics.OverallPassRate = float64(metrics.PassedTests) / float64(metrics.TotalTests) * 100
		metrics.OverallFailRate = float64(metrics.FailedTests) / float64(metrics.TotalTests) * 100
		metrics.OverallFlakyRate = float64(totalFlaky) / float64(metrics.TotalTests) * 100
		metrics.OverallSkipRate = float64(metrics.SkippedTests) / float64(metrics.TotalTests) * 100
	}

	// Convert trend map to slice and sort
	for _, trendPoint := range trendMap {
		trendPoint.FailRate = 100 - trendPoint.PassRate
		trendPoint.PassRate *= 100
		metrics.TrendData = append(metrics.TrendData, *trendPoint)
	}
	sort.Slice(metrics.TrendData, func(i, j int) bool {
		return metrics.TrendData[i].Date.Before(metrics.TrendData[j].Date)
	})

	// Calculate top failing, slowest, and most retried tests (reuse existing testAnalytics)
	metrics.TopFailingTests = s.getTopFailingTests(testAnalytics, 10)
	metrics.SlowestTests = s.getSlowestTests(testAnalytics, 10)
	metrics.MostRetriedTests = s.getMostRetriedTests(testAnalytics, 10)

	// Calculate file path metrics from test analytics
	metrics.FilePathMetrics = s.calculateFilePathMetrics(testAnalytics)

	return metrics
}

// calculateFilePathMetrics calculates metrics for each file path from test analytics
func (s *AnalyticsService) calculateFilePathMetrics(testAnalytics []valueobjects.TestAnalytics) map[string]valueobjects.FileMetrics {
	fileMetrics := make(map[string]valueobjects.FileMetrics)

	// Group tests by file path
	fileTestMap := make(map[string][]valueobjects.TestAnalytics)
	for _, test := range testAnalytics {
		if test.FilePath != "" {
			fileTestMap[test.FilePath] = append(fileTestMap[test.FilePath], test)
		}
	}

	// Calculate metrics for each file
	for filePath, tests := range fileTestMap {
		var totalDuration float64
		var passedTests, failedTests int

		for _, test := range tests {
			totalDuration += test.AverageDuration
			if test.PassRate > 50 { // Consider test as passed if pass rate > 50%
				passedTests++
			} else {
				failedTests++
			}
		}

		totalTests := len(tests)
		passRate := float64(0)
		if totalTests > 0 {
			passRate = float64(passedTests) / float64(totalTests) * 100
		}

		avgDuration := float64(0)
		if totalTests > 0 {
			avgDuration = totalDuration / float64(totalTests)
		}

		fileMetrics[filePath] = valueobjects.FileMetrics{
			FilePath:    filePath,
			TestCount:   totalTests,
			PassRate:    passRate,
			FailRate:    100 - passRate,
			AvgDuration: avgDuration,
		}
	}

	return fileMetrics
}

// fileHasMatchingTags checks if a test result file contains any tests with matching tags
func (s *AnalyticsService) fileHasMatchingTags(result entities.TestResult, filters *valueobjects.FilterCriteria) bool {
	if len(filters.Tags) == 0 {
		return true // No tag filter means all files match
	}

	// Check all suites in the file for matching tags
	for _, suite := range result.Suites {
		if s.suiteHasMatchingTags(suite, filters) {
			return true
		}
	}
	return false
}

// fileHasMatchingStatus checks if a test result file contains any tests with matching status
func (s *AnalyticsService) fileHasMatchingStatus(result entities.TestResult, filters *valueobjects.FilterCriteria) bool {
	if len(filters.Status) == 0 {
		return true // No status filter means all files match
	}

	// Check all suites in the file for matching status
	for _, suite := range result.Suites {
		if s.suiteHasMatchingStatus(suite, filters) {
			return true
		}
	}
	return false
}

// suiteHasMatchingTags recursively checks if a suite or its nested suites have matching tags
func (s *AnalyticsService) suiteHasMatchingTags(suite entities.Suite, filters *valueobjects.FilterCriteria) bool {
	// Check specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}
		if s.matchesTagFilters(spec, filters) {
			return true
		}
	}

	// Check nested suites
	for _, nestedSuite := range suite.Suites {
		if s.suiteHasMatchingTags(nestedSuite, filters) {
			return true
		}
	}

	return false
}

// suiteHasMatchingStatus recursively checks if a suite or its nested suites have tests with matching status
func (s *AnalyticsService) suiteHasMatchingStatus(suite entities.Suite, filters *valueobjects.FilterCriteria) bool {
	// Skip coverage-setup & coverage-teardown suites based on title
	if strings.Contains(suite.Title, "coverage.setup.ts") || strings.Contains(suite.Title, "coverage.teardown.ts") {
		return false
	}

	// Check specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		// Check tests in this spec
		for _, test := range spec.Tests {
			// Skip coverage tests based on project name
			if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
				continue
			}

			// Check if test status matches any of the filter statuses
			testStatus := test.Status
			// Apply status mapping
			if testStatus == "expected" {
				testStatus = "passed"
			} else if testStatus == "unexpected" || testStatus == "timeout" || testStatus == "timedOut" {
				testStatus = "failed"
			}

			for _, filterStatus := range filters.Status {
				if testStatus == filterStatus {
					return true
				}
			}
		}
	}

	// Check nested suites
	for _, nestedSuite := range suite.Suites {
		if s.suiteHasMatchingStatus(nestedSuite, filters) {
			return true
		}
	}

	return false
}

// FileCounts represents test counts for a single file
type FileCounts struct {
	Expected   int
	Skipped    int
	Unexpected int
	Flaky      int
}

// TestDurations represents duration calculations for a single file
type TestDurations struct {
	TotalDuration float64
	TestCount     int
}

// calculateFileTestCounts calculates test counts for a single file, excluding coverage tests
// This function properly traverses the Playwright JSON hierarchy: Suite->Spec->Test->TestResult
func (s *AnalyticsService) calculateFileTestCounts(result entities.TestResult, filters *valueobjects.FilterCriteria) FileCounts {
	counts := FileCounts{}

	// Process each suite in the file - this is the parent node as per requirements
	for _, suite := range result.Suites {
		s.countTestsInSuite(suite, &counts, filters)
	}

	return counts
}

// calculateTestDurations calculates total duration for all tests in a file, excluding coverage tests
// This function properly sums all test result durations (including retries) for each test
func (s *AnalyticsService) calculateTestDurations(result entities.TestResult, filters *valueobjects.FilterCriteria) TestDurations {
	durations := TestDurations{}

	// Process each suite in the file - this is the parent node as per requirements
	for _, suite := range result.Suites {
		s.calculateDurationsInSuite(suite, &durations, filters, result.Stats.StartTime)
	}

	return durations
}

// calculateDurationsInSuite recursively calculates durations for tests in a suite, excluding coverage tests
func (s *AnalyticsService) calculateDurationsInSuite(suite entities.Suite, durations *TestDurations, filters *valueobjects.FilterCriteria, statsStartTime time.Time) {
	// Skip coverage-setup & coverage-teardown suites based on title
	if strings.Contains(suite.Title, "coverage.setup.ts") || strings.Contains(suite.Title, "coverage.teardown.ts") {
		return
	}

	// Process specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		// Check if spec matches tag filters
		if !s.matchesTagFilters(spec, filters) {
			continue
		}

		// Process tests in this spec
		for _, test := range spec.Tests {
			// Skip coverage tests based on project name
			if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
				continue
			}

			// Apply status filter if specified
			if len(filters.Status) > 0 {
				testStatus := test.Status
				// Apply status mapping
				if testStatus == "expected" {
					testStatus = "passed"
				} else if testStatus == "unexpected" || testStatus == "timeout" || testStatus == "timedOut" {
					testStatus = "failed"
				}

				// Check if test status matches any of the filter statuses
				statusMatch := false
				for _, filterStatus := range filters.Status {
					if testStatus == filterStatus {
						statusMatch = true
						break
					}
				}
				if !statusMatch {
					continue // Skip this test if it doesn't match the status filter
				}
			}

			// Sum all result durations for this test (including all retries)
			var testTotalDuration float64
			for _, result := range test.Results {
				// Apply additional filters to individual results if needed
				if s.shouldIncludeTestResult(result, filters, statsStartTime, test.ProjectName) {
					testTotalDuration += float64(result.Duration)
				}
			}

			// Only count tests that have at least one valid result
			if testTotalDuration > 0 {
				durations.TotalDuration += testTotalDuration
				durations.TestCount++
			}
		}
	}

	// Process nested suites recursively - this handles suites.suites structure
	for _, nestedSuite := range suite.Suites {
		s.calculateDurationsInSuite(nestedSuite, durations, filters, statsStartTime)
	}
}

// countTestsInSuite recursively counts tests in a suite, excluding coverage tests
// Properly handles the nested Suite->Spec->Test hierarchy
func (s *AnalyticsService) countTestsInSuite(suite entities.Suite, counts *FileCounts, filters *valueobjects.FilterCriteria) {
	// Skip coverage-setup & coverage-teardown suites based on title
	if strings.Contains(suite.Title, "coverage.setup.ts") || strings.Contains(suite.Title, "coverage.teardown.ts") {
		return
	}

	// Process specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		// Check if spec matches tag filters
		if !s.matchesTagFilters(spec, filters) {
			continue
		}

		// Count tests in this spec - each test represents one test case
		for _, test := range spec.Tests {
			// Skip coverage-setup and coverage-teardown projects
			if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
				continue
			}

			// Apply project name filters if specified
			if len(filters.ProjectName) > 0 {
				found := false
				for _, project := range filters.ProjectName {
					if strings.EqualFold(test.ProjectName, project) {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}

			// Apply status filter if specified
			if len(filters.Status) > 0 {
				testStatus := test.Status
				// Apply status mapping
				if testStatus == "expected" {
					testStatus = "passed"
				} else if testStatus == "unexpected" || testStatus == "timeout" || testStatus == "timedOut" {
					testStatus = "failed"
				}

				// Check if test status matches any of the filter statuses
				statusMatch := false
				for _, filterStatus := range filters.Status {
					if testStatus == filterStatus {
						statusMatch = true
						break
					}
				}
				if !statusMatch {
					continue // Skip this test if it doesn't match the status filter
				}
			}

			// Count based on test status - this is the key fix
			// We need to look at the actual test status, not the stats object
			// Also check for flaky tests by analyzing multiple results with different outcomes
			testStatus := test.Status

			// If test.Status is not "flaky", check if it should be considered flaky
			// by analyzing the results for different outcomes
			if testStatus != "flaky" && len(test.Results) > 1 {
				statusMap := make(map[string]bool)
				for _, result := range test.Results {
					normalizedStatus := result.Status
					if normalizedStatus == "expected" {
						normalizedStatus = "passed"
					} else if normalizedStatus == "unexpected" || normalizedStatus == "timeout" || normalizedStatus == "timedOut" {
						normalizedStatus = "failed"
					}
					statusMap[normalizedStatus] = true
				}

				// If we have both passed and failed results, it's flaky
				if (statusMap["passed"] && statusMap["failed"]) ||
				   (statusMap["passed"] && statusMap["skipped"]) ||
				   (len(statusMap) > 1 && statusMap["passed"]) {
					testStatus = "flaky"
				}
			}

			switch testStatus {
			case "expected":
				counts.Expected++
			case "skipped":
				counts.Skipped++
			case "unexpected", "timeout", "timedOut": // All these statuses are treated as failed
				counts.Unexpected++
			case "flaky":
				counts.Flaky++
			}
		}
	}

	// Process nested suites recursively - this handles suites.suites structure
	for _, nestedSuite := range suite.Suites {
		s.countTestsInSuite(nestedSuite, counts, filters)
	}
}

// Helper types and methods
type testExecution struct {
	Status      string
	Duration    int
	StartTime   time.Time
	ProjectName string
	Annotations []entities.Annotation
	Error       *entities.TestError
	FilePath    string
	TestName    string
	RetryCount  int
}

// processPlaywrightTestResult processes Playwright test results according to the new requirements
func (s *AnalyticsService) processPlaywrightTestResult(result entities.TestResult, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria) {
	// Only consider data from suites[] which is parent node
	for _, suite := range result.Suites {
		s.processPlaywrightSuiteWithRunFolder(suite, testMap, filters, result.FileName, result.RunFolderName, result.Stats.StartTime)
	}
}

// processPlaywrightTestResultConsolidated processes Playwright test results with consolidated reporting
// Tests are consolidated by testName + projectName combination across all JSON runs
func (s *AnalyticsService) processPlaywrightTestResultConsolidated(result entities.TestResult, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria) {
	// Only consider data from suites[] which is parent node
	for _, suite := range result.Suites {
		s.processPlaywrightSuiteWithRunFolderConsolidated(suite, testMap, filters, result.FileName, result.RunFolderName, result.Stats.StartTime)
	}
}

func (s *AnalyticsService) processPlaywrightSuite(suite entities.Suite, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, startTime time.Time) {
	s.processPlaywrightSuiteWithTitle(suite, "", testMap, filters, fileName, "", startTime)
}

func (s *AnalyticsService) processPlaywrightSuiteWithRunFolder(suite entities.Suite, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	s.processPlaywrightSuiteWithTitle(suite, "", testMap, filters, fileName, runFolderName, startTime)
}

func (s *AnalyticsService) processPlaywrightSuiteWithRunFolderConsolidated(suite entities.Suite, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	s.processPlaywrightSuiteWithTitleConsolidated(suite, "", testMap, filters, fileName, runFolderName, startTime)
}

func (s *AnalyticsService) processPlaywrightSuiteWithTitle(suite entities.Suite, parentSuiteTitle string, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	// Skip coverage-setup & coverage-teardown for test case count
	if strings.Contains(suite.Title, "coverage-setup") || strings.Contains(suite.Title, "coverage-teardown") {
		return
	}

	// Determine the current suite title for specs
	currentSuiteTitle := suite.Title
	if parentSuiteTitle != "" {
		currentSuiteTitle = parentSuiteTitle + " > " + suite.Title
	}

	// Process specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
	
			continue
		}

		s.processPlaywrightSpecWithSuite(spec, currentSuiteTitle, testMap, filters, fileName, runFolderName, startTime)
	}

	// Recursively process nested suites
	for _, subSuite := range suite.Suites {
		s.processPlaywrightSuiteWithTitle(subSuite, currentSuiteTitle, testMap, filters, fileName, runFolderName, startTime)
	}
}

func (s *AnalyticsService) processPlaywrightSuiteWithTitleConsolidated(suite entities.Suite, parentSuiteTitle string, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	// Skip coverage-setup & coverage-teardown for test case count
	if strings.Contains(suite.Title, "coverage-setup") || strings.Contains(suite.Title, "coverage-teardown") {
		return
	}

	// Determine the current suite title for specs
	currentSuiteTitle := suite.Title
	if parentSuiteTitle != "" {
		currentSuiteTitle = parentSuiteTitle + " > " + suite.Title
	}

	// Process specs in this suite
	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		s.processPlaywrightSpecWithSuiteConsolidated(spec, currentSuiteTitle, testMap, filters, fileName, runFolderName, startTime)
	}

	// Recursively process nested suites
	for _, subSuite := range suite.Suites {
		s.processPlaywrightSuiteWithTitleConsolidated(subSuite, currentSuiteTitle, testMap, filters, fileName, runFolderName, startTime)
	}
}

// processPlaywrightSpecWithSuite processes individual specs according to Playwright format requirements with suite title context
// Now calculates at run level (file level) instead of individual test execution level
func (s *AnalyticsService) processPlaywrightSpecWithSuite(spec entities.Spec, suiteTitle string, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	// Skip coverage tests based on spec title
	if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {

		return
	}

	// Create combined test name in 'suites.suites.title > specs.title' format as per requirements
	// This ensures test names are displayed as 'describe name > test name'
	combinedTestName := spec.Title
	if suiteTitle != "" {
		combinedTestName = suiteTitle + " > " + spec.Title
	}

	// Create unique key for test case using filename, file path, suite title, and spec title
	// This ensures uniqueness across different test result files and different suites within the same file
	key := fmt.Sprintf("%s::%s::%s::%s", fileName, spec.File, suiteTitle, spec.Title)

	// Check if spec matches tag filters first
	if !s.matchesTagFilters(spec, filters) {
		return
	}

	if testMap[key] == nil {
		testMap[key] = &valueobjects.TestAnalytics{
			TestName:        combinedTestName,
			FilePath:        spec.File,
			Tags:            spec.Tags,
			TestCreatedDate: startTime,
			TestUpdatedDate: startTime,
			RunStatusMap:    make(map[string]string), // Track status per run (fileName)
		}
	}

	ta := testMap[key]

	// Update dates based on stats.startTime
	if startTime.Before(ta.TestCreatedDate) {
		ta.TestCreatedDate = startTime
	}
	if startTime.After(ta.TestUpdatedDate) {
		ta.TestUpdatedDate = startTime
	}

	// Determine the final status for this test case in this run (file)
	var finalStatus string
	var totalDuration int
	var retryCount int
	var projectName string

	var errorMessages []string

	// Process all tests for this spec to determine the final status for this run
	// Use test.Status as the primary source of truth, as suggested
	for _, test := range spec.Tests {
		// Skip coverage-setup and coverage-teardown projects
		if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
			continue
		}

		projectName = test.ProjectName

		// Use test.Status as the primary source for final status
		if test.Status != "" {
			finalStatus = test.Status

			// If test.Status is not "flaky", check if it should be considered flaky
			// by analyzing the results for different outcomes
			if finalStatus != "flaky" && len(test.Results) > 1 {
				statusMap := make(map[string]bool)
				for _, result := range test.Results {
					normalizedStatus := result.Status
					if normalizedStatus == "expected" {
						normalizedStatus = "passed"
					} else if normalizedStatus == "unexpected" || normalizedStatus == "timeout" || normalizedStatus == "timedOut" {
						normalizedStatus = "failed"
					}
					statusMap[normalizedStatus] = true
				}

				// If we have both passed and failed results, it's flaky
				if (statusMap["passed"] && statusMap["failed"]) ||
				   (statusMap["passed"] && statusMap["skipped"]) ||
				   (len(statusMap) > 1 && statusMap["passed"]) {
					finalStatus = "flaky"
				}
			}

			// Apply status mapping for test.Status (after flaky detection)
			if finalStatus == "expected" {
				finalStatus = "passed"
			} else if finalStatus == "unexpected" || finalStatus == "timeout" || finalStatus == "timedOut" {
				finalStatus = "failed"
			}
		}

		// Find the last result for accumulating duration, retry count, etc.
		var lastResult *entities.TestExecution
		for i := len(test.Results) - 1; i >= 0; i-- {
			result := &test.Results[i]
			if s.shouldIncludeTestResult(*result, filters, startTime, test.ProjectName) {
				lastResult = result
				break
			}
		}

		if lastResult != nil {
			// Accumulate data for this run
			for _, result := range test.Results {
				if s.shouldIncludeTestResult(result, filters, startTime, test.ProjectName) {
					totalDuration += result.Duration
					retryCount += result.Retry
					if result.Error != nil {
						errorMessages = append(errorMessages, result.Error.Message)
					}
				}
			}

			// Process all attachments for S3 keys (videos, screenshots, logs, traces, etc.)
			var allAttachmentS3Keys []map[string]string
			var videoS3Key string
			var screenshotS3Keys []string

			if len(lastResult.Attachments) > 0 {
				// Process all attachments and create S3 keys
				allAttachmentS3Keys = s.extractAllAttachmentS3Keys(lastResult.Attachments, runFolderName)

				// Extract specific video and screenshot keys for backward compatibility
				for _, attachmentInfo := range allAttachmentS3Keys {
					if attachmentInfo["type"] == "video" && videoS3Key == "" {
						videoS3Key = attachmentInfo["s3Key"]
					} else if attachmentInfo["type"] == "screenshot" {
						screenshotS3Keys = append(screenshotS3Keys, attachmentInfo["s3Key"])
					}
				}

				// Set attachment fields in TestAnalytics
				if videoS3Key != "" {
					ta.VideoS3Key = videoS3Key
				}
				if len(screenshotS3Keys) > 0 {
					ta.ScreenshotS3Keys = screenshotS3Keys
				}
				// Set all attachment S3 keys for comprehensive access
				if len(allAttachmentS3Keys) > 0 {
					ta.AllAttachmentS3Keys = allAttachmentS3Keys
				}

				// Convert attachments to TestRunAttachment format
				var attachments []valueobjects.TestRunAttachment
				for _, att := range lastResult.Attachments {
					attachments = append(attachments, valueobjects.TestRunAttachment{
						Name:        att.Name,
						ContentType: att.ContentType,
						Path:        att.Path,
					})
				}
				ta.Attachments = attachments
			}
		}

		// We found a valid test, break to process this spec
		if finalStatus != "" {
			break
		}
	}

	// Always try to determine a final status if we don't have one
	if finalStatus == "" {
		// Look at the first valid test to determine status
		for _, test := range spec.Tests {
			if test.ProjectName != "coverage-setup" && test.ProjectName != "coverage-teardown" {
				// Try test.Status first, then fall back to result status
				if test.Status != "" {
					finalStatus = test.Status
				} else if len(test.Results) > 0 {
					// Use the status from the first result
					finalStatus = test.Results[0].Status
				}

				// Apply status mapping
				if finalStatus == "expected" {
					finalStatus = "passed"
				} else if finalStatus == "unexpected" || finalStatus == "timeout" || finalStatus == "timedOut" {
					finalStatus = "failed"
				}

				if finalStatus != "" {
					break
				}
			}
		}
	}

	// If we still don't have a final status, set a default based on the first test
	if finalStatus == "" {
		for _, test := range spec.Tests {
			if test.ProjectName != "coverage-setup" && test.ProjectName != "coverage-teardown" {
				if len(test.Results) > 0 {
					finalStatus = test.Results[0].Status
					// Apply status mapping
					if finalStatus == "expected" {
						finalStatus = "passed"
					} else if finalStatus == "unexpected" || finalStatus == "timeout" || finalStatus == "timedOut" {
						finalStatus = "failed"
					}
					break
				}
			}
		}
	}

	// If we STILL don't have a status, default to "skipped"
	if finalStatus == "" {
		finalStatus = "skipped"
	}

	// Always process the test (we should always have a finalStatus now)
	// Initialize RunStatusMap if it's nil
	if ta.RunStatusMap == nil {
		ta.RunStatusMap = make(map[string]string)
	}

	// Store the status for this run (fileName)
	ta.RunStatusMap[fileName] = finalStatus
	ta.ProjectName = projectName

	ta.ErrorMessages = append(ta.ErrorMessages, errorMessages...)

	// Update retry count (total across all runs)
	ta.RetryCount += retryCount

	// Calculate running average duration
	currentRunCount := len(ta.RunStatusMap)
	if ta.AverageDuration == 0 {
		ta.AverageDuration = float64(totalDuration)
	} else {
		ta.AverageDuration = (ta.AverageDuration*float64(currentRunCount-1) + float64(totalDuration)) / float64(currentRunCount)
	}

	// Update total runs and calculate rates
	ta.TotalRuns = currentRunCount

	// Count status occurrences across all runs
	var passCount, failCount, flakyCount, skippedCount int
	for _, status := range ta.RunStatusMap {
		switch status {
		case "passed":
			passCount++
		case "failed":
			failCount++
		case "flaky":
			flakyCount++
		case "skipped":
			skippedCount++
		}
	}

	// Calculate rates based on run counts
	if ta.TotalRuns > 0 {
		ta.PassRate = float64(passCount) / float64(ta.TotalRuns) * 100
		ta.FailRate = float64(failCount) / float64(ta.TotalRuns) * 100
		ta.FlakyRate = float64(flakyCount) / float64(ta.TotalRuns) * 100
		ta.SkippedRate = float64(skippedCount) / float64(ta.TotalRuns) * 100

		// Set last status - prioritize flaky if any flaky tests exist
		if flakyCount > 0 {
			ta.LastStatus = "flaky"
		} else if failCount > 0 {
			ta.LastStatus = "failed"
		} else if passCount > 0 {
			ta.LastStatus = "passed"
		} else if skippedCount > 0 {
			ta.LastStatus = "skipped"
		}


	}
}

// processPlaywrightSpecWithSuiteConsolidated processes individual specs with consolidated reporting
// Tests are consolidated by testName + projectName combination across all JSON runs
func (s *AnalyticsService) processPlaywrightSpecWithSuiteConsolidated(spec entities.Spec, suiteTitle string, testMap map[string]*valueobjects.TestAnalytics, filters *valueobjects.FilterCriteria, fileName string, runFolderName string, startTime time.Time) {
	// Skip coverage tests based on spec title
	if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
		return
	}

	// Create combined test name in 'suites.suites.title > specs.title' format as per requirements
	// This ensures test names are displayed as 'describe name > test name'
	combinedTestName := spec.Title
	if suiteTitle != "" {
		combinedTestName = suiteTitle + " > " + spec.Title
	}

	// Check if spec matches tag filters first
	if !s.matchesTagFilters(spec, filters) {
		return
	}

	// Process all tests for this spec - each test should be treated individually
	for testIndex, test := range spec.Tests {
		// Skip coverage-setup and coverage-teardown projects
		if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
			continue
		}

		// Create unique test name for each test within the spec
		// If there are multiple tests in the spec, add test index to make them unique
		uniqueTestName := combinedTestName
		if len(spec.Tests) > 1 {
			uniqueTestName = fmt.Sprintf("%s [Test %d]", combinedTestName, testIndex+1)
		}

		// Create unique key for consolidated reporting: testName + projectName
		// This ensures tests are consolidated across all JSON runs for the same test+project combination
		key := fmt.Sprintf("%s::%s", uniqueTestName, test.ProjectName)

		if testMap[key] == nil {
			testMap[key] = &valueobjects.TestAnalytics{
				TestName:        uniqueTestName,
				FilePath:        spec.File,
				ProjectName:     test.ProjectName,
				Tags:            spec.Tags,
				TestCreatedDate: startTime,
				TestUpdatedDate: startTime,
				RunStatusMap:    make(map[string]string), // Track status per run (fileName)
			}
		}

		ta := testMap[key]

		// Update dates based on stats.startTime
		if startTime.Before(ta.TestCreatedDate) {
			ta.TestCreatedDate = startTime
		}
		if startTime.After(ta.TestUpdatedDate) {
			ta.TestUpdatedDate = startTime
		}

		// Determine the final status for this test case in this run (file)
		var finalStatus string
		var totalDuration int
		var retryCount int
		var errorMessages []string

		// Determine status based on test.Status first, then fall back to results analysis

		// First, check if test.Status is available and use it
		if test.Status != "" {
			finalStatus = test.Status

			// If test.Status is not "flaky", check if it should be considered flaky
			// by analyzing the results for different outcomes
			if finalStatus != "flaky" && len(test.Results) > 1 {
				statusMap := make(map[string]bool)
				for _, result := range test.Results {
					normalizedStatus := result.Status
					if normalizedStatus == "expected" {
						normalizedStatus = "passed"
					} else if normalizedStatus == "unexpected" || normalizedStatus == "timeout" || normalizedStatus == "timedOut" {
						normalizedStatus = "failed"
					}
					statusMap[normalizedStatus] = true
				}

				// If we have both passed and failed results, it's flaky
				if (statusMap["passed"] && statusMap["failed"]) ||
				   (statusMap["passed"] && statusMap["skipped"]) ||
				   (len(statusMap) > 1 && statusMap["passed"]) {
					finalStatus = "flaky"
				}
			}
		} else if len(test.Results) > 0 {
			// Fallback to analyzing results if test.Status is not available
			expectedStatus := test.ExpectedStatus
			actualStatus := test.Results[0].Status

			// Apply status mapping for actual status
			if actualStatus == "expected" {
				actualStatus = "passed"
			} else if actualStatus == "unexpected" || actualStatus == "timeout" || actualStatus == "timedOut" {
				actualStatus = "failed"
			}

			finalStatus = actualStatus

			// Debug logging for first few tests
			if len(testMap) < 5 {
				fmt.Printf("DEBUG: Test %s, Project: %s, Expected: %s, Actual: %s\n",
					uniqueTestName, test.ProjectName, expectedStatus, actualStatus)
			}
		} else {
			// If no results available, default to skipped
			finalStatus = "skipped"
		}

		// Apply final status mapping
		if finalStatus == "expected" {
			finalStatus = "passed"
		} else if finalStatus == "unexpected" || finalStatus == "timeout" || finalStatus == "timedOut" {
			finalStatus = "failed"
		}

		// Find the last result for accumulating duration, retry count, etc.
		var lastResult *entities.TestExecution
		for i := len(test.Results) - 1; i >= 0; i-- {
			result := &test.Results[i]
			if s.shouldIncludeTestResult(*result, filters, startTime, test.ProjectName) {
				lastResult = result
				break
			}
		}

		if lastResult != nil {
			// Accumulate data for this run
			for _, result := range test.Results {
				if s.shouldIncludeTestResult(result, filters, startTime, test.ProjectName) {
					totalDuration += result.Duration
					retryCount += result.Retry
					if result.Error != nil {
						errorMessages = append(errorMessages, result.Error.Message)
					}
				}
			}

			// Process all attachments for S3 keys (videos, screenshots, logs, traces, etc.)
			var allAttachmentS3Keys []map[string]string
			var videoS3Key string
			var screenshotS3Keys []string

			if len(lastResult.Attachments) > 0 {
				// Process all attachments and create S3 keys
				allAttachmentS3Keys = s.extractAllAttachmentS3Keys(lastResult.Attachments, runFolderName)

				// Extract specific video and screenshot keys for backward compatibility
				for _, attachmentInfo := range allAttachmentS3Keys {
					if attachmentInfo["type"] == "video" && videoS3Key == "" {
						videoS3Key = attachmentInfo["s3Key"]
					} else if attachmentInfo["type"] == "screenshot" {
						screenshotS3Keys = append(screenshotS3Keys, attachmentInfo["s3Key"])
					}
				}

				// Set attachment fields in TestAnalytics
				if videoS3Key != "" {
					ta.VideoS3Key = videoS3Key
				}
				if len(screenshotS3Keys) > 0 {
					ta.ScreenshotS3Keys = screenshotS3Keys
				}
				// Set all attachment S3 keys for comprehensive access
				if len(allAttachmentS3Keys) > 0 {
					ta.AllAttachmentS3Keys = allAttachmentS3Keys
				}

				// Convert attachments to TestRunAttachment format
				var attachments []valueobjects.TestRunAttachment
				for _, att := range lastResult.Attachments {
					attachments = append(attachments, valueobjects.TestRunAttachment{
						Name:        att.Name,
						ContentType: att.ContentType,
						Path:        att.Path,
					})
				}
				ta.Attachments = attachments
			}
		}

		// Fallback logic if we still don't have a final status
		if finalStatus == "" {
			if len(test.Results) > 0 {
				actualStatus := test.Results[0].Status
				// Apply status mapping
				if actualStatus == "expected" {
					actualStatus = "passed"
				} else if actualStatus == "unexpected" || actualStatus == "timeout" || actualStatus == "timedOut" {
					actualStatus = "failed"
				}
				finalStatus = actualStatus
			}
		}

		// If we STILL don't have a status, default to "skipped"
		if finalStatus == "" {
			finalStatus = "skipped"
		}

		// Always process the test (we should always have a finalStatus now)
		// Initialize RunStatusMap if it's nil
		if ta.RunStatusMap == nil {
			ta.RunStatusMap = make(map[string]string)
		}

		// Store the status for this run (fileName)
		ta.RunStatusMap[fileName] = finalStatus

		ta.ErrorMessages = append(ta.ErrorMessages, errorMessages...)

		// Update retry count (total across all runs)
		ta.RetryCount += retryCount

		// Calculate running average duration
		currentRunCount := len(ta.RunStatusMap)
		if ta.AverageDuration == 0 {
			ta.AverageDuration = float64(totalDuration)
		} else {
			ta.AverageDuration = (ta.AverageDuration*float64(currentRunCount-1) + float64(totalDuration)) / float64(currentRunCount)
		}

		// Update total runs and calculate rates
		ta.TotalRuns = currentRunCount

		// Count status occurrences across all runs
		var passCount, failCount, flakyCount, skippedCount int
		for _, status := range ta.RunStatusMap {
			switch status {
			case "passed":
				passCount++
			case "failed":
				failCount++
			case "flaky":
				flakyCount++
			case "skipped":
				skippedCount++
			}
		}

		// Calculate rates based on run counts
		if ta.TotalRuns > 0 {
			ta.PassRate = float64(passCount) / float64(ta.TotalRuns) * 100
			ta.FailRate = float64(failCount) / float64(ta.TotalRuns) * 100
			ta.FlakyRate = float64(flakyCount) / float64(ta.TotalRuns) * 100
			ta.SkippedRate = float64(skippedCount) / float64(ta.TotalRuns) * 100

			// Set last status to the current run's status (most recent)
			// This ensures we use the actual status from this specific run
			ta.LastStatus = finalStatus
		}

		// Continue processing all tests in this spec
		// Each test should be processed individually, not just the first one
	}
}

// Helper methods for Playwright format
func (s *AnalyticsService) matchesPlaywrightFilters(result entities.TestExecution, filters *valueobjects.FilterCriteria, statsStartTime time.Time, projectName string) bool {
	if filters == nil {
		return true
	}

	// Date filter should work for stats.startTime
	if filters.StartDate != nil && statsStartTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && statsStartTime.After(*filters.EndDate) {
		return false
	}

	// Status filter should use effective status (considering flaky tests)
	if len(filters.Status) > 0 {
		// Determine the effective status for this test result
		effectiveStatus := result.Status
		if result.Retry > 0 && (result.Status == "passed" || result.Status == "expected") {
			effectiveStatus = "flaky"
		} else if result.Status == "expected" {
			effectiveStatus = "passed"
		} else if result.Status == "unexpected" || result.Status == "timeout" || result.Status == "timedOut" {
			effectiveStatus = "failed"
		}

		found := false
		for _, status := range filters.Status {
			if effectiveStatus == status {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Project filter - only guac and zGPU
	if len(filters.ProjectName) > 0 {
		found := false
		for _, project := range filters.ProjectName {
			if strings.EqualFold(projectName, project) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}



	return true
}

// Helper method that matches all filters except status - used for initial filtering
func (s *AnalyticsService) matchesPlaywrightFiltersExceptStatus(result entities.TestExecution, filters *valueobjects.FilterCriteria, statsStartTime time.Time, projectName string) bool {
	if filters == nil {
		return true
	}

	// Date filter should work for stats.startTime
	if filters.StartDate != nil && statsStartTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && statsStartTime.After(*filters.EndDate) {
		return false
	}

	// Project filter - only guac and zGPU
	if len(filters.ProjectName) > 0 {
		found := false
		for _, project := range filters.ProjectName {
			if strings.EqualFold(projectName, project) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}



	return true
}

// shouldIncludeTestResult determines if a test result should be included based on filters
// This method is more lenient and only applies filters when they are actually provided
func (s *AnalyticsService) shouldIncludeTestResult(result entities.TestExecution, filters *valueobjects.FilterCriteria, statsStartTime time.Time, projectName string) bool {

	// If no filters are provided, include all tests
	if filters == nil {
		return true
	}

	// Only apply date filter if it's actually set
	if filters.StartDate != nil && statsStartTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && statsStartTime.After(*filters.EndDate) {
		return false
	}

	// Status filter should use effective status (considering flaky tests)
	if len(filters.Status) > 0 {
		// Determine the effective status for this test result
		effectiveStatus := result.Status
		if result.Retry > 0 && (result.Status == "passed" || result.Status == "expected") {
			effectiveStatus = "flaky"
		} else if result.Status == "expected" {
			effectiveStatus = "passed"
		} else if result.Status == "unexpected" || result.Status == "timeout" || result.Status == "timedOut" {
			effectiveStatus = "failed"
		}

		found := false
		for _, status := range filters.Status {
			if effectiveStatus == status {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Only apply project filter if it's actually set
	if len(filters.ProjectName) > 0 {
		found := false
		for _, project := range filters.ProjectName {
			if strings.EqualFold(projectName, project) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// If we get here, the test result passes all applicable filters
	return true
}

// Helper method to check if spec matches tag filters
func (s *AnalyticsService) matchesTagFilters(spec entities.Spec, filters *valueobjects.FilterCriteria) bool {
	if filters == nil || len(filters.Tags) == 0 {
		return true
	}

	// Check if any of the spec's tags match the filter tags
	for _, filterTag := range filters.Tags {
		for _, specTag := range spec.Tags {
			if specTag == filterTag {
				return true
			}
		}
	}
	return false
}





func (s *AnalyticsService) categorizeError(errorMessage string) string {
	errorMessage = strings.ToLower(errorMessage)

	// Timeout related errors
	if strings.Contains(errorMessage, "timeout") || strings.Contains(errorMessage, "timed out") {
		return "Timeout"
	}

	// Connection related errors
	if strings.Contains(errorMessage, "connection") || strings.Contains(errorMessage, "connect") ||
	   strings.Contains(errorMessage, "refused") || strings.Contains(errorMessage, "unreachable") {
		return "Connection"
	}

	// Element/selector related errors
	if strings.Contains(errorMessage, "element") || strings.Contains(errorMessage, "selector") ||
	   strings.Contains(errorMessage, "locator") || strings.Contains(errorMessage, "not found") ||
	   strings.Contains(errorMessage, "not visible") || strings.Contains(errorMessage, "not attached") {
		return "Element Not Found"
	}

	// Assertion/expectation errors
	if strings.Contains(errorMessage, "assertion") || strings.Contains(errorMessage, "expect") ||
	   strings.Contains(errorMessage, "should") || strings.Contains(errorMessage, "failed") {
		return "Assertion"
	}

	// Network related errors
	if strings.Contains(errorMessage, "network") || strings.Contains(errorMessage, "http") ||
	   strings.Contains(errorMessage, "request") || strings.Contains(errorMessage, "response") ||
	   strings.Contains(errorMessage, "fetch") {
		return "Network"
	}

	// Navigation errors
	if strings.Contains(errorMessage, "navigation") || strings.Contains(errorMessage, "navigate") ||
	   strings.Contains(errorMessage, "page") || strings.Contains(errorMessage, "load") {
		return "Navigation"
	}

	// Permission/security errors
	if strings.Contains(errorMessage, "permission") || strings.Contains(errorMessage, "security") ||
	   strings.Contains(errorMessage, "blocked") || strings.Contains(errorMessage, "denied") {
		return "Permission"
	}

	return "Other"
}

// GetErrorCategoriesWithTests returns detailed error categories with associated failed tests
func (s *AnalyticsService) GetErrorCategoriesWithTests(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.ErrorCategoryDetail {
	// Get all test analytics to find failed tests
	testAnalytics := s.CalculateTestAnalytics(testResults, filters)

	// Group failed tests by error category
	categoryMap := make(map[string][]valueobjects.TestAnalytics)

	for _, test := range testAnalytics {
		// Only include tests that have failures
		if test.FailRate > 0 && len(test.ErrorMessages) > 0 {
			// Categorize each error message and associate the test with those categories
			categoriesForTest := make(map[string]bool)

			for _, errorMsg := range test.ErrorMessages {
				category := s.categorizeError(errorMsg)
				categoriesForTest[category] = true
			}

			// Add test to each category it belongs to
			for category := range categoriesForTest {
				categoryMap[category] = append(categoryMap[category], test)
			}
		}
	}

	// Convert map to slice and sort by count
	var result []valueobjects.ErrorCategoryDetail
	for category, tests := range categoryMap {
		// Sort tests within category by fail rate (descending)
		sort.Slice(tests, func(i, j int) bool {
			return tests[i].FailRate > tests[j].FailRate
		})

		result = append(result, valueobjects.ErrorCategoryDetail{
			Category:    category,
			Count:       len(tests),
			FailedTests: tests,
		})
	}

	// Sort categories by count (descending)
	sort.Slice(result, func(i, j int) bool {
		return result[i].Count > result[j].Count
	})

	return result
}

// extractPlaywrightTestExecutions extracts test executions according to Playwright format requirements
func (s *AnalyticsService) extractPlaywrightTestExecutions(result entities.TestResult, filters *valueobjects.FilterCriteria) []testExecution {
	var executions []testExecution

	for _, suite := range result.Suites {
		executions = append(executions, s.extractFromPlaywrightSuite(suite, filters, result.Stats.StartTime)...)
	}

	return executions
}

func (s *AnalyticsService) extractFromPlaywrightSuite(suite entities.Suite, filters *valueobjects.FilterCriteria, statsStartTime time.Time) []testExecution {
	return s.extractFromPlaywrightSuiteWithTitle(suite, "", filters, statsStartTime)
}

func (s *AnalyticsService) extractFromPlaywrightSuiteWithTitle(suite entities.Suite, parentSuiteTitle string, filters *valueobjects.FilterCriteria, statsStartTime time.Time) []testExecution {
	var executions []testExecution

	// Skip coverage-setup & coverage-teardown for test case count
	if strings.Contains(suite.Title, "coverage-setup") || strings.Contains(suite.Title, "coverage-teardown") {
		return executions
	}

	// Determine the current suite title for specs
	currentSuiteTitle := suite.Title
	if parentSuiteTitle != "" {
		currentSuiteTitle = parentSuiteTitle + " > " + suite.Title
	}

	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		// Check if spec matches tag filters first
		if !s.matchesTagFilters(spec, filters) {
			continue
		}

		// Create combined test name: Suite Title > Spec Title
		combinedTestName := spec.Title
		if currentSuiteTitle != "" {
			combinedTestName = currentSuiteTitle + " > " + spec.Title
		}

		for _, test := range spec.Tests {
			// Skip coverage-setup and coverage-teardown projects
			if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
				continue
			}

			for _, testResult := range test.Results {
				if s.matchesPlaywrightFilters(testResult, filters, statsStartTime, test.ProjectName) {
					// Determine if test is flaky
					status := testResult.Status

					// Check if test should be considered flaky based on multiple results with different outcomes
					if test.Status == "flaky" {
						status = "flaky"
					} else if len(test.Results) > 1 {
						// Check for flaky pattern: multiple results with different outcomes
						statusMap := make(map[string]bool)
						for _, result := range test.Results {
							normalizedStatus := result.Status
							if normalizedStatus == "expected" {
								normalizedStatus = "passed"
							} else if normalizedStatus == "unexpected" || normalizedStatus == "timeout" || normalizedStatus == "timedOut" {
								normalizedStatus = "failed"
							}
							statusMap[normalizedStatus] = true
						}

						// If we have both passed and failed results, it's flaky
						if (statusMap["passed"] && statusMap["failed"]) ||
						   (statusMap["passed"] && statusMap["skipped"]) ||
						   (len(statusMap) > 1 && statusMap["passed"]) {
							status = "flaky"
						}
					}

					// Handle 'expected' status as 'passed' for non-flaky tests
					if status == "expected" {
						status = "passed"
					}
					// Handle 'unexpected', 'timeout', and 'timedOut' as 'failed'
					if status == "unexpected" || status == "timeout" || status == "timedOut" {
						status = "failed"
					}

					executions = append(executions, testExecution{
						Status:      status,
						Duration:    testResult.Duration,
						StartTime:   statsStartTime, // Use stats.startTime for date filtering
						ProjectName: test.ProjectName,
						Annotations: testResult.Annotations,
						Error:       testResult.Error,
						FilePath:    spec.File,
						TestName:    combinedTestName,
						RetryCount:  testResult.Retry,
					})
				}
			}
		}
	}

	// Recursively process nested suites
	for _, subSuite := range suite.Suites {
		executions = append(executions, s.extractFromPlaywrightSuiteWithTitle(subSuite, currentSuiteTitle, filters, statsStartTime)...)
	}

	return executions
}

// Helper method to check if a date matches the date filters
func (s *AnalyticsService) matchesDateFilters(startTime time.Time, filters *valueobjects.FilterCriteria) bool {
	if filters == nil {
		return true
	}

	// Date filter should work for stats.startTime
	if filters.StartDate != nil && startTime.Before(*filters.StartDate) {
		return false
	}
	if filters.EndDate != nil && startTime.After(*filters.EndDate) {
		return false
	}

	return true
}

// Helper method to count tests by project from the test result structure
func (s *AnalyticsService) countTestsByProject(result entities.TestResult, projectCounts map[string]int, filters *valueobjects.FilterCriteria) {
	// Use a map to track unique test cases per project to avoid double counting
	uniqueTests := make(map[string]map[string]bool) // project -> test key -> exists

	for _, suite := range result.Suites {
		s.countProjectsFromSuite(suite, uniqueTests, filters, result.Stats.StartTime)
	}

	// Convert unique test counts to project counts
	for project, tests := range uniqueTests {
		projectCounts[project] += len(tests)
	}
}

// Helper method to recursively count projects from suite structure
func (s *AnalyticsService) countProjectsFromSuite(suite entities.Suite, uniqueTests map[string]map[string]bool, filters *valueobjects.FilterCriteria, statsStartTime time.Time) {
	// Skip coverage-setup & coverage-teardown for test case count
	if strings.Contains(suite.Title, "coverage-setup") || strings.Contains(suite.Title, "coverage-teardown") {
		return
	}

	for _, spec := range suite.Specs {
		// Skip coverage tests based on spec title
		if spec.Title == "build coverage reports" || spec.Title == "setup coverage folder" {
			continue
		}

		// Check if spec matches tag filters first
		if !s.matchesTagFilters(spec, filters) {
			continue
		}

		for _, test := range spec.Tests {
			// Skip coverage-setup and coverage-teardown projects
			if test.ProjectName == "coverage-setup" || test.ProjectName == "coverage-teardown" {
				continue
			}

			// Apply project filters
			if len(filters.ProjectName) > 0 {
				found := false
				for _, project := range filters.ProjectName {
					if strings.EqualFold(test.ProjectName, project) {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}

			// Create unique key for this test case
			testKey := fmt.Sprintf("%s::%s", spec.File, spec.Title)

			// Initialize project map if it doesn't exist
			if uniqueTests[test.ProjectName] == nil {
				uniqueTests[test.ProjectName] = make(map[string]bool)
			}

			// Mark this test as counted for this project
			uniqueTests[test.ProjectName][testKey] = true
			break // Only count once per spec
		}
	}

	// Recursively process nested suites
	for _, subSuite := range suite.Suites {
		s.countProjectsFromSuite(subSuite, uniqueTests, filters, statsStartTime)
	}
}

func (s *AnalyticsService) getTopFailingTests(analytics []valueobjects.TestAnalytics, limit int) []valueobjects.TestAnalytics {
	sort.Slice(analytics, func(i, j int) bool {
		return analytics[i].FailRate > analytics[j].FailRate
	})

	if len(analytics) > limit {
		return analytics[:limit]
	}
	return analytics
}

func (s *AnalyticsService) getSlowestTests(analytics []valueobjects.TestAnalytics, limit int) []valueobjects.TestAnalytics {
	sort.Slice(analytics, func(i, j int) bool {
		return analytics[i].AverageDuration > analytics[j].AverageDuration
	})

	if len(analytics) > limit {
		return analytics[:limit]
	}
	return analytics
}

func (s *AnalyticsService) getMostRetriedTests(analytics []valueobjects.TestAnalytics, limit int) []valueobjects.TestAnalytics {
	sort.Slice(analytics, func(i, j int) bool {
		return analytics[i].RetryCount > analytics[j].RetryCount
	})

	if len(analytics) > limit {
		return analytics[:limit]
	}
	return analytics
}

// extractVideoS3Key extracts video S3 key from attachments or constructs it from filename
func (s *AnalyticsService) extractVideoS3Key(attachments []entities.Attachment, fileName string) string {
	// First, check if video path is in attachments
	for _, attachment := range attachments {
		if strings.Contains(strings.ToLower(attachment.ContentType), "video") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".mp4") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".webm") {

			// If path looks like an S3 key with your bucket structure, return it
			if strings.Contains(attachment.Path, "workflowtest/dev/ourl-lemon/") ||
			   strings.Contains(attachment.Path, "dev/ourl-lemon/") ||
			   strings.Contains(attachment.Path, "playwright-report/data/") ||
			   strings.Contains(attachment.Path, "test-videos/") {
				return attachment.Path
			}

			// If it's a local path with hash-based filename, construct S3 key
			// Video files are named like: 0054d4ea29a2947049dd703035c7134e978a5f64.webm
			if attachment.Path != "" {
				log.Printf("DEBUG: Processing video attachment - Path: %s, Name: %s, ContentType: %s", attachment.Path, attachment.Name, attachment.ContentType)
				s3Key := s.constructVideoS3Key(attachment.Path, fileName)
				log.Printf("DEBUG: Constructed video S3 key: %s from path: %s, fileName: %s", s3Key, attachment.Path, fileName)
				return s3Key
			}
		}
	}

	// Only return empty string if no video attachment found
	// Don't construct S3 key from filename unless there's an actual video attachment
	return ""
}

// extractVideoS3KeyWithRunFolder extracts video S3 key using the provided run folder name
func (s *AnalyticsService) extractVideoS3KeyWithRunFolder(attachments []entities.Attachment, runFolderName string, testInfo map[string]interface{}) string {
	// Find video attachment
	for _, attachment := range attachments {
		if strings.Contains(strings.ToLower(attachment.ContentType), "video") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".mp4") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".webm") {

			// Extract video filename from path
			videoFileName := filepath.Base(attachment.Path)

			// Extract test folder name from attachment path
			// Example: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU-retry2/video.webm
			var testFolderName string
			if strings.Contains(attachment.Path, "/test-results/") {
				parts := strings.Split(attachment.Path, "/test-results/")
				if len(parts) > 1 {
					testFolderParts := strings.Split(parts[1], "/")
					if len(testFolderParts) > 0 {
						testFolderName = testFolderParts[0]
					}
				}
			}

			// Construct S3 key using the provided run folder name
			if runFolderName != "" && testFolderName != "" {
				s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolderName, testFolderName, videoFileName)
				return s3Key
			}
		}
	}

	return ""
}

// extractScreenshotS3KeysWithRunFolder extracts screenshot S3 keys using the provided run folder name
func (s *AnalyticsService) extractScreenshotS3KeysWithRunFolder(attachments []entities.Attachment, runFolderName string) []string {
	var screenshotKeys []string

	for _, attachment := range attachments {
		if strings.Contains(strings.ToLower(attachment.ContentType), "image") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".png") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".jpg") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".jpeg") {

			// Extract screenshot filename from path
			screenshotFileName := filepath.Base(attachment.Path)

			// Extract test folder name from attachment path
			var testFolderName string
			if strings.Contains(attachment.Path, "/test-results/") {
				parts := strings.Split(attachment.Path, "/test-results/")
				if len(parts) > 1 {
					testFolderParts := strings.Split(parts[1], "/")
					if len(testFolderParts) > 0 {
						testFolderName = testFolderParts[0]
					}
				}
			}

			// Construct S3 key using the provided run folder name
			if runFolderName != "" && testFolderName != "" {
				s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolderName, testFolderName, screenshotFileName)
				screenshotKeys = append(screenshotKeys, s3Key)
			}
		}
	}

	return screenshotKeys
}

// extractScreenshotS3Keys extracts screenshot S3 keys from attachments
func (s *AnalyticsService) extractScreenshotS3Keys(attachments []entities.Attachment, fileName string) []string {
	var screenshotKeys []string

	for _, attachment := range attachments {
		if strings.Contains(strings.ToLower(attachment.ContentType), "image") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".png") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".jpg") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".jpeg") {

			// Skip expected/baseline screenshots from __snapshots__ directory
			if strings.Contains(attachment.Path, "__snapshots__") {
				continue
			}

			// Only include actual screenshots and diff screenshots from test-results
			if !strings.Contains(attachment.Path, "/test-results/") {
				continue
			}

			var s3Key string
			// If path looks like an S3 key with your bucket structure, use it
			if strings.Contains(attachment.Path, "workflowtest/dev/ourl-lemon/") ||
			   strings.Contains(attachment.Path, "dev/ourl-lemon/") ||
			   strings.Contains(attachment.Path, "playwright-report/data/") ||
			   strings.Contains(attachment.Path, "test-screenshots/") ||
			   strings.Contains(attachment.Path, "screenshots/") {
				s3Key = attachment.Path
				log.Printf("Using direct screenshot S3 path: %s", s3Key)
			} else if attachment.Path != "" {
				// Construct S3 key from local path
				s3Key = s.constructScreenshotS3Key(attachment.Path, fileName)
				log.Printf("Constructed screenshot S3 key: %s from path: %s, fileName: %s", s3Key, attachment.Path, fileName)
			}

			if s3Key != "" {
				screenshotKeys = append(screenshotKeys, s3Key)
			}
		}
	}

	return screenshotKeys
}

// constructVideoS3Key constructs S3 key for video based on local path and filename
func (s *AnalyticsService) constructVideoS3Key(localPath, fileName string) string {
	// Extract filename from local path and construct S3 key
	videoFileName := filepath.Base(localPath)

	// Extract run folder from JSON filename
	runFolder := s.extractRunFolderFromFileName(fileName)

	// Extract test folder name from the JSON path
	// JSON path format: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU/video-1.webm
	// We need to extract: banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU
	var testFolderName string

	// Try to extract test folder name from different path formats
	if strings.Contains(localPath, "/e2e/test-results/") {
		// Format: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU/video-1.webm
		parts := strings.Split(localPath, "/e2e/test-results/")
		if len(parts) > 1 {
			afterTestResults := parts[1]
			testFolderParts := strings.Split(afterTestResults, "/")
			if len(testFolderParts) > 0 {
				testFolderName = testFolderParts[0]
			}
		}
	} else if strings.Contains(localPath, "/test-results/") {
		// Format: /test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU-retry1/video-1.webm
		parts := strings.Split(localPath, "/test-results/")
		if len(parts) > 1 {
			afterTestResults := parts[1]
			testFolderParts := strings.Split(afterTestResults, "/")
			if len(testFolderParts) > 0 {
				testFolderName = testFolderParts[0]
			}
		}
	} else if strings.Contains(localPath, "/") && !strings.HasPrefix(localPath, "/") {
		// Format: test-folder-name/video-1.webm (relative path)
		pathParts := strings.Split(localPath, "/")
		if len(pathParts) > 1 {
			testFolderName = pathParts[0]
		}
	}

	// If we still couldn't extract test folder name, we have a problem
	// For S3 data, we need the actual test folder name to construct the correct path
	if testFolderName == "" {
		// Try to derive test folder name from the video filename
		// Video files are often named like: video-1.webm, video.webm, etc.
		// But the test folder name should be derived from the test context
		testFolderName = s.deriveTestFolderFromVideoFilename(videoFileName)
		log.Printf("WARNING: Could not extract test folder name from path: %s, derived: %s", localPath, testFolderName)
	}

	// Construct the S3 key for the video file
	// Format: workflowtest/dev/ourl-lemon/{run-folder}/test-results/{test-folder-name}/{video-file}
	s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolder, testFolderName, videoFileName)

	return s3Key
}

// constructScreenshotS3Key constructs S3 key for screenshot based on local path and filename
func (s *AnalyticsService) constructScreenshotS3Key(localPath, fileName string) string {
	if localPath == "" {
		return ""
	}

	// Extract filename from local path and construct S3 key
	screenshotFileName := filepath.Base(localPath)

	// Extract run folder from JSON filename
	runFolder := s.extractRunFolderFromFileName(fileName)

	// For S3 data, screenshots are stored in test-results/{test-folder-name}/ not playwright-report/data/
	// The localPath can have different formats:
	// 1. /test-results/test-folder-name/screenshot.png
	// 2. test-folder-name/screenshot.png
	// 3. screenshot.png (just the filename)

	var testFolderName string

	// Try to extract test folder name from different path formats
	if strings.Contains(localPath, "/e2e/test-results/") {
		// Format: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU/screenshot.png
		parts := strings.Split(localPath, "/e2e/test-results/")
		if len(parts) > 1 {
			afterTestResults := parts[1]
			testFolderParts := strings.Split(afterTestResults, "/")
			if len(testFolderParts) > 0 {
				testFolderName = testFolderParts[0]
			}
		}
	} else if strings.Contains(localPath, "/test-results/") {
		// Format: /test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU-retry1/screenshot.png
		parts := strings.Split(localPath, "/test-results/")
		if len(parts) > 1 {
			afterTestResults := parts[1]
			testFolderParts := strings.Split(afterTestResults, "/")
			if len(testFolderParts) > 0 {
				testFolderName = testFolderParts[0]
			}
		}
	} else if strings.Contains(localPath, "/") && !strings.HasPrefix(localPath, "/") {
		// Format: test-folder-name/screenshot.png (relative path)
		pathParts := strings.Split(localPath, "/")
		if len(pathParts) > 1 {
			testFolderName = pathParts[0]
		}
	}

	// If we still couldn't extract test folder name, try to derive it from the filename
	if testFolderName == "" {
		// Try to extract from the screenshot filename if it contains test info
		if strings.Contains(screenshotFileName, "-") {
			// Use the screenshot filename without extension as test folder name
			testFolderName = strings.TrimSuffix(screenshotFileName, filepath.Ext(screenshotFileName))
		} else {
			testFolderName = "unknown-test"
		}
	}

	// Construct the S3 key for the screenshot file
	// Format: workflowtest/dev/ourl-lemon/{run-folder}/test-results/{test-folder-name}/{screenshot-file}
	s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolder, testFolderName, screenshotFileName)

	return s3Key
}

// extractRunFolderFromFileName extracts the run folder name from the JSON filename
// JSON files come from paths like: workflowtest/dev/ourl-lemon/2025-07-14T16:04:23Z-pw-e2e-test-workflow-template-jp4xx-e2e-test-3699202517/playwright-report/json-results/test-results.json
// We need to extract: 2025-07-14T16:04:23Z-pw-e2e-test-workflow-template-jp4xx-e2e-test-3699202517
func (s *AnalyticsService) extractRunFolderFromFileName(fileName string) string {

	// If the fileName contains path separators, it might be a full S3 key
	if strings.Contains(fileName, "/") {
		// Try to extract the run folder from the full path
		// Look for the pattern: ourl-lemon/{run-folder}/playwright-report/
		if strings.Contains(fileName, "ourl-lemon/") && strings.Contains(fileName, "/playwright-report/") {
			parts := strings.Split(fileName, "ourl-lemon/")
			if len(parts) > 1 {
				afterOurlLemon := parts[1]
				playwrightIndex := strings.Index(afterOurlLemon, "/playwright-report/")
				if playwrightIndex > 0 {
					runFolder := afterOurlLemon[:playwrightIndex]
					return runFolder
				}
			}
		}
	}

	// If fileName is just the base name (e.g., "test-results.json"), we can't extract the run folder
	// In this case, we'll use the fileName without extension as a fallback
	baseName := strings.TrimSuffix(fileName, ".json")

	// For locally uploaded files, we need to construct a proper run folder name
	// The baseName format is like: "2025-07-16T06:11:04Z-test-results"
	// We need to construct a proper S3-style run folder name
	if strings.Contains(baseName, "T") && strings.Contains(baseName, "Z") {
		// Extract timestamp part (e.g., "2025-07-16T06:11:04Z")
		timestampMatch := regexp.MustCompile(`(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z)`).FindString(baseName)
		if timestampMatch != "" {
			// For locally uploaded files, construct a run folder name that matches actual S3 structure
			// Based on the actual S3 structure: 2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-3475518139
			// We'll use the known pattern for this specific timestamp
			if timestampMatch == "2025-07-16T06:11:04Z" {
				return "2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-3475518139"
			}
			// For other timestamps, use a generic pattern (this would need to be updated for new test runs)
			return fmt.Sprintf("%s-pw-originalurl-nightly-1752604200-89hhm-e2e-test-3475518139", timestampMatch)
		}
	}

	// Fallback: use the base filename without extension
	return baseName
}

// deriveTestFolderFromVideoFilename attempts to derive a test folder name from video filename
// This is a fallback when we can't extract the test folder from the attachment path
func (s *AnalyticsService) deriveTestFolderFromVideoFilename(videoFileName string) string {
	// For standard video files like video-1.webm, video.webm, we can't derive much
	// This is a limitation - we need the attachment path to contain the test folder name
	if videoFileName == "video.webm" || videoFileName == "video-1.webm" {
		return "unknown-test"
	}

	// If the video filename contains test-specific information, use it
	// Remove the extension and use as test folder name
	baseName := strings.TrimSuffix(videoFileName, filepath.Ext(videoFileName))
	if baseName != "video" && baseName != "video-1" {
		return baseName
	}

	return "unknown-test"
}

// extractVideoS3KeyWithTestInfo extracts video S3 key with additional test context
func (s *AnalyticsService) extractVideoS3KeyWithTestInfo(attachments []entities.Attachment, fileName string, testInfo map[string]interface{}) string {
	// First try the standard extraction
	s3Key := s.extractVideoS3Key(attachments, fileName)

	// If the standard extraction resulted in "unknown-test", try to use test context
	if strings.Contains(s3Key, "unknown-test") {
		// Try to construct a better test folder name from the test context
		if testName, ok := testInfo["testName"].(string); ok {
			// Use the test name to construct a more accurate S3 key
			s3Key = s.constructVideoS3KeyFromTestName(attachments, fileName, testName, testInfo)
		}
	}

	return s3Key
}

// constructVideoS3KeyFromTestName constructs S3 key using test name as fallback
func (s *AnalyticsService) constructVideoS3KeyFromTestName(attachments []entities.Attachment, fileName, testName string, testInfo map[string]interface{}) string {
	// Extract run folder from JSON filename
	runFolder := s.extractRunFolderFromFileName(fileName)

	// Find video attachment
	var videoFileName string
	for _, attachment := range attachments {
		if strings.Contains(strings.ToLower(attachment.ContentType), "video") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".mp4") ||
			strings.HasSuffix(strings.ToLower(attachment.Path), ".webm") {
			videoFileName = filepath.Base(attachment.Path)
			break
		}
	}

	if videoFileName == "" {
		videoFileName = "video.webm" // fallback
	}

	// Try to construct test folder name from test name
	// Test names like "banner-persistent-banner-E-684e1--Verify-links-are-clickable-Guac"
	// might map to S3 folders like "banner-persistent-banner-E-684e1--Verify-links-are-clickable-Guac"
	testFolderName := testName

	// Add retry suffix if retry > 0
	if retry, ok := testInfo["retry"].(int); ok && retry > 0 {
		testFolderName = fmt.Sprintf("%s-retry%d", testFolderName, retry)
	}

	// Construct the S3 key
	s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolder, testFolderName, videoFileName)

	log.Printf("DEBUG: Constructed S3 key from test name: %s (testName: %s, retry: %v)", s3Key, testName, testInfo["retry"])

	return s3Key
}

// extractAllAttachmentS3Keys extracts S3 keys for all attachments (videos, screenshots, logs, traces, etc.)
func (s *AnalyticsService) extractAllAttachmentS3Keys(attachments []entities.Attachment, runFolderName string) []map[string]string {
	var allAttachments []map[string]string

	for _, attachment := range attachments {
		// Extract filename from path
		fileName := filepath.Base(attachment.Path)

		// Extract test folder name from attachment path
		// Example: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU/video.webm
		var testFolderName string
		if strings.Contains(attachment.Path, "/test-results/") {
			parts := strings.Split(attachment.Path, "/test-results/")
			if len(parts) > 1 {
				testFolderParts := strings.Split(parts[1], "/")
				if len(testFolderParts) > 0 {
					testFolderName = testFolderParts[0]
				}
			}
		}

		// Determine attachment type based on content type and file extension
		attachmentType := s.determineAttachmentType(attachment)

		// Construct S3 key using the provided run folder name
		if runFolderName != "" && testFolderName != "" {
			s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolderName, testFolderName, fileName)

			attachmentInfo := map[string]string{
				"name":        attachment.Name,
				"type":        attachmentType,
				"contentType": attachment.ContentType,
				"fileName":    fileName,
				"s3Key":       s3Key,
				"path":        attachment.Path,
			}
			allAttachments = append(allAttachments, attachmentInfo)
		}
	}

	return allAttachments
}

// extractAllAttachmentsForTest extracts all attachments (videos, logs, traces, etc.) for a test
func (s *AnalyticsService) extractAllAttachmentsForTest(attachments []entities.Attachment, fileName string) []map[string]string {
	var allAttachments []map[string]string

	// Extract run folder from JSON filename
	runFolder := s.extractRunFolderFromFileName(fileName)

	for _, attachment := range attachments {
		if attachment.Path == "" {
			continue
		}

		// Extract test folder name from the attachment path
		var testFolderName string
		if strings.Contains(attachment.Path, "/e2e/test-results/") {
			// Format: /e2e/test-results/banner-persistent-banner-E-aaa2f-avigations-During-a-Session-zGPU/video-1.webm
			parts := strings.Split(attachment.Path, "/e2e/test-results/")
			if len(parts) > 1 {
				testFolderParts := strings.Split(parts[1], "/")
				if len(testFolderParts) > 0 {
					testFolderName = testFolderParts[0]
				}
			}
		} else if strings.Contains(attachment.Path, "/test-results/") {
			// Format: /test-results/test-folder-name/file.ext
			parts := strings.Split(attachment.Path, "/test-results/")
			if len(parts) > 1 {
				testFolderParts := strings.Split(parts[1], "/")
				if len(testFolderParts) > 0 {
					testFolderName = testFolderParts[0]
				}
			}
		}

		if testFolderName == "" {
			continue
		}

		// Extract filename from path
		fileName := filepath.Base(attachment.Path)

		// Determine attachment type based on content type and file extension
		attachmentType := s.determineAttachmentType(attachment)

		// Construct S3 key
		s3Key := fmt.Sprintf("workflowtest/dev/ourl-lemon/%s/test-results/%s/%s", runFolder, testFolderName, fileName)

		attachmentInfo := map[string]string{
			"name":        attachment.Name,
			"type":        attachmentType,
			"contentType": attachment.ContentType,
			"fileName":    fileName,
			"s3Key":       s3Key,
			"path":        attachment.Path,
		}
		allAttachments = append(allAttachments, attachmentInfo)
	}

	return allAttachments
}

// determineAttachmentType determines the type of attachment based on content type and file extension
func (s *AnalyticsService) determineAttachmentType(attachment entities.Attachment) string {
	contentType := strings.ToLower(attachment.ContentType)
	path := strings.ToLower(attachment.Path)
	name := strings.ToLower(attachment.Name)

	// Video files
	if strings.Contains(contentType, "video") ||
		strings.HasSuffix(path, ".mp4") ||
		strings.HasSuffix(path, ".webm") ||
		name == "video" {
		return "video"
	}

	// Screenshot/Image files
	if strings.Contains(contentType, "image") ||
		strings.HasSuffix(path, ".png") ||
		strings.HasSuffix(path, ".jpg") ||
		strings.HasSuffix(path, ".jpeg") ||
		strings.Contains(name, "screenshot") {
		return "screenshot"
	}

	// Trace files
	if strings.Contains(contentType, "application/zip") ||
		strings.HasSuffix(path, ".zip") ||
		name == "trace" {
		return "trace"
	}

	// Log files
	if strings.Contains(contentType, "text/plain") ||
		strings.HasSuffix(path, ".log") ||
		strings.Contains(name, "log") ||
		name == "controls-ws-logs" {
		return "log"
	}

	// Error context files
	if strings.Contains(contentType, "text/markdown") ||
		strings.HasSuffix(path, ".md") ||
		name == "error-context" {
		return "error-context"
	}

	// Default to generic attachment
	return "attachment"
}
